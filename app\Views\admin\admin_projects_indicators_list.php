<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#impact-indicators" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-left me-2"></i>
    Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators/create') ?>" class="btn btn-primary promis-btn-gradient">
    <i class="bi bi-graph-up me-2"></i>
    Add Indicator
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 fw-bold text-primary mb-2">
            <i class="bi bi-graph-up me-2"></i>
            Impact Indicators
        </h1>
        <p class="text-muted mb-0">
            Measurable impact metrics for project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Indicator Statistics -->
<div class="row g-4 mb-4">
    <!-- Total Indicators -->
    <div class="col-lg-3 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-graph-up text-primary"></i>
                </div>
                <div class="h4 fw-bold text-primary mb-2">
                    <?= $indicatorStats['total_indicators'] ?>
                </div>
                <div class="text-muted small">Total Indicators</div>
            </div>
        </div>
    </div>

    <!-- With Actual Values -->
    <div class="col-lg-3 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-check-circle text-success"></i>
                </div>
                <div class="h4 fw-bold text-success mb-2">
                    <?= $indicatorStats['with_actual_values'] ?>
                </div>
                <div class="text-muted small">With Actual Values</div>
            </div>
        </div>
    </div>

    <!-- Average Baseline -->
    <div class="col-lg-3 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-bar-chart text-info"></i>
                </div>
                <div class="h4 fw-bold text-info mb-2">
                    <?= number_format($indicatorStats['averages']['baseline'], 2) ?>
                </div>
                <div class="text-muted small">Avg Baseline</div>
            </div>
        </div>
    </div>

    <!-- Average Target -->
    <div class="col-lg-3 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="display-6 mb-3">
                    <i class="bi bi-bullseye text-warning"></i>
                </div>
                <div class="h4 fw-bold text-warning mb-2">
                    <?= number_format($indicatorStats['averages']['target'], 2) ?>
                </div>
                <div class="text-muted small">Avg Target</div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Summary -->
<?php if (!empty($indicatorPerformance)): ?>
<div class="card mb-4">
    <div class="card-header">
        📈 Performance Summary
    </div>
    <div class="card-body">
        <div class="row g-3">
            <?php
            $statusCounts = ['pending' => 0, 'achieved' => 0, 'exceeded' => 0, 'not_met' => 0];
            foreach ($indicatorPerformance as $perf) {
                $statusCounts[$perf['status']]++;
            }
            ?>
            <div class="col-lg-3 col-md-6">
                <div class="bg-light rounded-3 p-3 text-center">
                    <div class="fw-bold text-muted mb-1">
                        Pending
                    </div>
                    <div class="text-secondary fs-5 fw-semibold">
                        <?= $statusCounts['pending'] ?>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="bg-light rounded-3 p-3 text-center">
                    <div class="fw-bold text-success mb-1">
                        Achieved
                    </div>
                    <div class="text-secondary fs-5 fw-semibold">
                        <?= $statusCounts['achieved'] ?>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="bg-light rounded-3 p-3 text-center">
                    <div class="fw-bold text-primary mb-1">
                        Exceeded
                    </div>
                    <div class="text-secondary fs-5 fw-semibold">
                        <?= $statusCounts['exceeded'] ?>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="bg-light rounded-3 p-3 text-center">
                    <div class="fw-bold text-danger mb-1">
                        Not Met
                    </div>
                    <div class="text-secondary fs-5 fw-semibold">
                        <?= $statusCounts['not_met'] ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Indicators Table -->
<div class="card">
    <div class="card-header">
        📊 Impact Indicators
    </div>

    <?php if (!empty($indicators)): ?>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th class="fw-semibold">
                            Indicator Description
                        </th>
                        <th class="text-center fw-semibold">
                            Baseline
                        </th>
                        <th class="text-center fw-semibold">
                            Target
                        </th>
                        <th class="text-center fw-semibold">
                            Actual
                        </th>
                        <th class="text-center fw-semibold">
                            Status
                        </th>
                        <th class="text-center fw-semibold">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($indicators as $indicator): ?>
                        <tr>
                            <td class="align-top">
                                <div class="fw-semibold text-dark mb-1">
                                    <?= esc($indicator['indicator_text']) ?>
                                </div>
                                <div class="small text-muted">
                                    Created: <?= date('M j, Y', strtotime($indicator['created_at'])) ?>
                                </div>
                            </td>
                            <td class="text-center align-top">
                                <?php if ($indicator['baseline_value'] !== null && $indicator['baseline_value'] !== ''): ?>
                                    <div class="fw-semibold text-dark">
                                        <?= is_numeric($indicator['baseline_value']) ? number_format($indicator['baseline_value'], 2) : esc($indicator['baseline_value']) ?>
                                    </div>
                                    <?php if ($indicator['baseline_date']): ?>
                                        <div class="small text-muted">
                                            <?= date('M j, Y', strtotime($indicator['baseline_date'])) ?>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted fst-italic">Not set</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center align-top">
                                <?php if ($indicator['target_value'] !== null && $indicator['target_value'] !== ''): ?>
                                    <div class="fw-semibold text-warning">
                                        <?= is_numeric($indicator['target_value']) ? number_format($indicator['target_value'], 2) : esc($indicator['target_value']) ?>
                                    </div>
                                    <?php if ($indicator['target_date']): ?>
                                        <div class="small text-muted">
                                            <?= date('M j, Y', strtotime($indicator['target_date'])) ?>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted fst-italic">Not set</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center align-top">
                                <?php if ($indicator['actual_value'] !== null && $indicator['actual_value'] !== ''): ?>
                                    <div class="fw-semibold text-success">
                                        <?= is_numeric($indicator['actual_value']) ? number_format($indicator['actual_value'], 2) : esc($indicator['actual_value']) ?>
                                    </div>
                                    <?php if ($indicator['actual_date']): ?>
                                        <div class="small text-muted">
                                            <?= date('M j, Y', strtotime($indicator['actual_date'])) ?>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted fst-italic">Pending</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center align-top">
                                <?php
                                $performance = null;
                                foreach ($indicatorPerformance as $perf) {
                                    if ($perf['id'] == $indicator['id']) {
                                        $performance = $perf;
                                        break;
                                    }
                                }
                                
                                if ($performance):
                                    $statusBadges = [
                                        'pending' => 'bg-secondary',
                                        'achieved' => 'bg-success',
                                        'exceeded' => 'bg-primary',
                                        'not_met' => 'bg-danger'
                                    ];
                                    $statusBadge = $statusBadges[$performance['status']] ?? 'bg-secondary';
                                ?>
                                    <span class="badge <?= $statusBadge ?> text-uppercase">
                                        <?= esc(str_replace('_', ' ', $performance['status'])) ?>
                                    </span>
                                    <?php if ($performance['achievement_rate'] !== null): ?>
                                        <div class="small text-muted mt-1">
                                            <?= number_format($performance['achievement_rate'], 1) ?>%
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted fst-italic">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center align-top">
                                <div class="d-flex gap-1 justify-content-center">
                                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators/' . $indicator['id'] . '/edit') ?>" class="btn btn-sm btn-outline-secondary">
                                        ✏️ Edit
                                    </a>
                                    <button onclick="showDeleteModal(<?= $indicator['id'] ?>, '<?= esc($indicator['indicator_text']) ?>')" class="btn btn-sm btn-outline-danger">
                                        🗑️ Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="text-center p-5 text-muted">
            <div class="display-1 mb-3">📊</div>
            <h3 class="text-secondary mb-2">No Impact Indicators Yet</h3>
            <p class="mb-4">Start defining measurable impact metrics for monitoring and evaluation.</p>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/indicators/create') ?>" class="btn btn-primary">
                📊 Create First Indicator
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Delete Impact Indicator</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the indicator "<span id="deleteIndicatorText" class="fw-semibold"></span>"? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">Delete Indicator</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentDeleteIndicatorId = null;
let deleteModal;

document.addEventListener('DOMContentLoaded', function() {
    deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
});

function showDeleteModal(indicatorId, indicatorText) {
    currentDeleteIndicatorId = indicatorId;
    document.getElementById('deleteIndicatorText').textContent = indicatorText;
    deleteModal.show();
}

function confirmDelete() {
    if (currentDeleteIndicatorId) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url('admin/projects/' . $project['id'] . '/indicators/') ?>' + currentDeleteIndicatorId + '/delete';

        // Add CSRF token if available
        <?php if (csrf_token()): ?>
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);
        <?php endif; ?>

        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?= $this->endSection() ?>
