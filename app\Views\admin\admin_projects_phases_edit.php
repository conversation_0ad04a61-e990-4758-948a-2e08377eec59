<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-phases" class="btn btn-secondary">
    ← Back to Project Profile
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="text-dark fs-2 fw-bold mb-2">
            Edit Phase
        </h1>
        <p class="text-muted mb-0">
            Editing phase: <strong><?= esc($phase['title']) ?></strong> in project: <strong><?= esc($project['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Edit Phase Form -->
<div class="card">
    <div class="card-header">
        📋 Phase Information
    </div>

    <div class="card-body p-4">
        <form method="post" action="<?= base_url('admin/projects/' . $project['id'] . '/phases/' . $phase['id'] . '/edit') ?>" class="phase-edit-form">
            <?= csrf_field() ?>

            <div class="row g-4 mb-4">

                <!-- Left Column -->
                <div class="col-md-6">
                    <!-- Phase Code -->
                    <div class="mb-4">
                        <label for="phase_code" class="form-label fw-semibold">
                            Phase Code <span class="text-danger">*</span>
                        </label>
                        <input type="text"
                               id="phase_code"
                               name="phase_code"
                               class="form-control border-danger"
                               value="<?= old('phase_code', $phase['phase_code']) ?>"
                               placeholder="e.g., PH001, INIT, PLAN"
                               required>
                        <div class="form-text">
                            Unique identifier for this phase (max 20 characters)
                        </div>
                    </div>

                    <!-- Phase Title -->
                    <div class="mb-4">
                        <label for="title" class="form-label fw-semibold">
                            Phase Title <span class="text-danger">*</span>
                        </label>
                        <input type="text"
                               id="title"
                               name="title"
                               class="form-control border-danger"
                               value="<?= old('title', $phase['title']) ?>"
                               placeholder="e.g., Planning Phase, Implementation Phase"
                               required>
                        <div class="form-text">
                            Descriptive name for this phase (max 150 characters)
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="mb-4">
                        <label for="status" class="form-label fw-semibold">
                            Status <span class="text-danger">*</span>
                        </label>
                        <select id="status"
                                name="status"
                                class="form-select border-danger"
                                required>
                            <option value="">Select Status</option>
                            <option value="active" <?= old('status', $phase['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="deactivated" <?= old('status', $phase['status']) === 'deactivated' ? 'selected' : '' ?>>Deactivated</option>
                        </select>
                        <div class="form-text">
                            Current status of this phase
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="col-md-6">
                    <!-- Start Date -->
                    <div class="mb-4">
                        <label for="start_date" class="form-label fw-semibold">
                            Start Date
                        </label>
                        <input type="date"
                               id="start_date"
                               name="start_date"
                               class="form-control"
                               value="<?= old('start_date', $phase['start_date']) ?>">
                        <div class="form-text">
                            When this phase is scheduled to start
                        </div>
                    </div>

                    <!-- End Date -->
                    <div class="mb-4">
                        <label for="end_date" class="form-label fw-semibold">
                            End Date
                        </label>
                        <input type="date"
                               id="end_date"
                               name="end_date"
                               class="form-control"
                               value="<?= old('end_date', $phase['end_date']) ?>">
                        <div class="form-text">
                            When this phase is scheduled to end
                        </div>
                    </div>

                    <!-- Phase Metadata -->
                    <div class="bg-light rounded-3 p-3">
                        <h4 class="text-dark fs-6 fw-semibold mb-2">
                            Phase Metadata
                        </h4>
                        <div class="row g-1 small">
                            <div class="col-4">
                                <span class="text-muted">Sort Order:</span>
                            </div>
                            <div class="col-8">
                                <span class="text-secondary"><?= esc($phase['sort_order']) ?></span>
                            </div>
                            
                            <div class="col-4">
                                <span class="text-muted">Created:</span>
                            </div>
                            <div class="col-8">
                                <span class="text-secondary"><?= date('M j, Y', strtotime($phase['created_at'])) ?></span>
                            </div>
                            
                            <?php if ($phase['updated_at'] && $phase['updated_at'] !== $phase['created_at']): ?>
                                <div class="col-4">
                                    <span class="text-muted">Updated:</span>
                                </div>
                                <div class="col-8">
                                    <span class="text-secondary"><?= date('M j, Y', strtotime($phase['updated_at'])) ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div class="mb-4">
                <label for="description" class="form-label fw-semibold">
                    Description
                </label>
                <textarea id="description"
                          name="description"
                          class="form-control"
                          rows="4"
                          placeholder="Detailed description of this phase, its objectives, and key activities..."><?= old('description', $phase['description']) ?></textarea>
                <div class="form-text">
                    Optional detailed description of the phase
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex gap-2 justify-content-end pt-3 border-top">
                <a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    ✅ Update Phase
                </button>
            </div>
        </form>
    </div>
</div>


<?= $this->endSection() ?>
